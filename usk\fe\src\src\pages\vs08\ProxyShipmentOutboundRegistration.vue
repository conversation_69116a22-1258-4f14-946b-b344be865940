<template>
  <!-- Because this page has different header title with the other pages
    so we need to render the header title in the page itself
    instead of using the default header title with layout -->
  <div class="tw:h-full tw:flex tw:flex-col tw:transform tw:-translate-y-4">
    <!-- #region header -->
    <div :class="`tw:tl:flex tw:tl:justify-between tw:tl:items-center`">
      <!-- Breadcrumbs -->
      <q-breadcrumbs
        class="tw:text-xs-design tw:font-bold tw:text-blue-3 tw:border-gray tw:py-3 tw:tl:py-0"
        active-color="tw:text-blue-3"
      >
        <template v-slot:separator>
          <q-icon size="1.5em" name="chevron_right" class="tw:text-[#7E8093]" />
        </template>
        <q-breadcrumbs-el
          label="トップ"
          :to="{
            name: 'home',
          }"
        />
        <q-breadcrumbs-el
          class="tw:cursor-pointer"
          label="入荷登録"
          :to="{
            name: 'proxyShipmentOutboundRegistration',
          }"
        />
        <div class="tw:ml-2 tw:flex tw:items-center tw:justify-center tw:text-black">
          <span class="tw:text-[#7E8093] tw:pb-1 tw:pl-4 tw:pr-5">|</span>
          <span>入荷登録入力</span>
        </div>
      </q-breadcrumbs>
      <div class="tw:flex tw:items-center tw:justify-between tw:tl:max-w-[60%] tw:py-5 tw:tl:py-0">
        <span
          v-if="proxyUser?.license_number"
          :class="`tw:text-m-design
            tw:mr-6 tw:flex-shrink-0 tw:flex`"
        >
          <span
            class="tw:max-w-[100px] tw:tl:max-w-[200px] tw:truncate tw:block tw:text-[#333333]"
            >{{ proxyUser?.license_number }}</span
          >
        </span>
        <span v-else />
        <span class="tw:font-[700] tw:text-xl-design tw:truncate">{{
          `${proxyUser?.name || ''}`
        }}</span>
      </div>
    </div>
    <!-- #endregion -->
    <div class="tw:flex-1 tw:flex tw:flex-col tw:pb-[13rem] tw:tl:pb-[7rem]">
      <StepCustom v-model="step" class="tw:pb-3" />
      <q-tab-panels v-model="step" animated class="tw:text-[#333333] tw:flex-1">
        <q-tab-panel :name="STEP_ENUM.STEP_1" class="tw:p-4 tw:tl:py-0">
          <h2 :class="`tw:text-xs-design tw:tl:font-bold tw:py-3`">
            必要項目を入力して「次に進む」ボタンを押してください。
          </h2>
          <form
            class="tw:grid tw:grid-cols-1 tw:gap-y-5 tw:tl:grid-cols-2 tw:gap-x-12 tw:tl:gap-y-2"
          >
            <!-- 漁獲/荷口番号 -->
            <div>
              <BaseLabel label="漁獲/荷口番号" isRequired />
              <div class="tw:flex tw:text-xl-design tw:gap-2 tw:text-[#333333] tw:items-start">
                <div class="tw:shrink-0">{{ maskMainCode(form.mainCode) }}</div>
                <BaseInput
                  v-model="form.subCode"
                  autocomplete="nope"
                  maxlength="3"
                  inputmode="numeric"
                  outlined
                  input-class="tw:text-xl-design tw:text-[#333333]"
                  class="tw:w-full vs tw:mt-0.5"
                  :error="!!errorStep1.subCode"
                  :error-message="errorStep1.subCode"
                  :mask="{
                    mask: /^\d{0,3}$/,
                  }"
                >
                </BaseInput>
              </div>
            </div>
            <!-- 出荷先 -->
            <div>
              <BaseLabel label="出荷先" />
              <span :class="`tw:block tw:text-xl-design tw:py-1`">
                {{ user.name || '' }}
              </span>
            </div>
            <!-- 出荷日 -->
            <div>
              <BaseLabel label="出荷日" isRequired />
              <BaseDatePicker
                v-model="form.date"
                class="tw:flex-1"
                input-class="tw:text-xl-design tw:text-[#333333]"
                :class="{
                  'tw:sm:mb-[0.5rem] tw:tl:mb-[1.3rem]': !!errorStep1.date,
                }"
                :error="!!errorStep1.date"
                :error-message="errorStep1.date"
              />
            </div>
            <!-- 出荷登録単位 -->
            <div>
              <BaseLabel label="出荷登録単位" />
              <q-radio
                v-for="(item, index) in INPUT_VOLUME_TYPE_OPTIONS"
                v-model="form.volumeType"
                :key="index"
                :val="item.value"
                size="5rem"
                class="tw:transform tw:-translate-x-5"
                @click="handleChangeVolumeType(item.value)"
              >
                <span class="tw:text-xl-design tw:text-[#333333]">
                  {{ item.label }}
                </span>
              </q-radio>
            </div>
            <!-- 単価 -->
            <div class="tw:col-span-1 tw:tl:col-end-3 tw:pb-8">
              <BaseLabel label="単価" />
              <div @click="handleClickInputPrice">
                <BaseSingleSelectInput
                  id="price-input-id"
                  class="price-item"
                  :options="priceOptions"
                  :error="!!errorStep1.price"
                  :error-message="errorStep1.price"
                  hide-selected
                  :use-input="false"
                  :clearable="false"
                  v-model="form.price"
                >
                  <template v-slot:default>
                    <InputPrise
                      class="input-price tw:bg-white tw:min-w-full"
                      input-class="tw:text-xl-design tw:text-right"
                      inputmode="numeric"
                      type="text"
                      option-value="value"
                      autocomplete="nope"
                      maxlength="9"
                      v-model="form.price"
                      borderless
                      @update:model-value="handleInputPrice"
                      :mask="{
                        mask: Number,
                        min: 0,
                        thousandsSeparator: ',',
                        scale: 2,
                        radix: '.',
                        lazy: false,
                      }"
                    >
                      <template v-slot:append>
                        <div class="tw:flex tw:items-end tw:gap-3">
                          <span class="tw:text-[#333333] tw:text-xs-design">
                            {{ form.volumeType === VOLUME_TYPE_ENUM.WEIGHT ? '万円/kg' : '円/尾' }}
                          </span>
                        </div>
                      </template>
                    </InputPrise>
                  </template>
                </BaseSingleSelectInput>
              </div>
            </div>
          </form>
        </q-tab-panel>
        <q-tab-panel :name="STEP_ENUM.STEP_2" class="tw:p-4 tw:tl:py-0">
          <h2 :class="`tw:text-xs-design tw:tl:font-bold tw:py-3`">
            必要項目を入力して「次に進む」ボタンを押してください。
          </h2>
          <form class="tw:grid tw:grid-cols-1 tw:tl:grid-cols-2 tw:gap-x-12 tw:gap-y-2">
            <div v-if="form.volumeType === VOLUME_TYPE_ENUM.WEIGHT" class="tw:mb-8 tw:tl:mb-0">
              <!-- 全体重量 -->
              <BaseLabel label="全体重量" isRequired />
              <BaseInput
                clearable
                type="text"
                maxlength="12"
                inputmode="numeric"
                autocomplete="nope"
                :model-value="form.grossWeight"
                @update:model-value="handleInputGrossWeight"
                :mask="{
                  mask: Number,
                  min: 0,
                  thousandsSeparator: ',',
                  scale: 2,
                  radix: '.',
                  lazy: false,
                  max: 9999999.99,
                }"
                outlined
                class="tw:!h-[7rem]"
                input-class="tw:text-right tw:text-xl-design"
                :error="!!errorStep2.grossWeight"
                :error-message="errorStep2.grossWeight"
              >
                <template v-slot:append>
                  <div :class="`tw:text-[#333333] tw:pr-4 tw:tl:pr-8 tw:text-m-design tw:mt-2`">
                    g
                  </div>
                </template>
              </BaseInput>
            </div>
            <div
              v-if="form.volumeType === VOLUME_TYPE_ENUM.WEIGHT"
              class="tw:flex tw:items-end tw:relative tw:mb-8 tw:tl:mb-0"
            >
              <!-- subtraction sign sm -->
              <div class="tw:h-1 tw:bg-[#7E8093] tw:px-4 tw:mb-8 tw:mx-5 tw:tl:hidden" />
              <div class="tw:flex-1">
                <!-- subtraction sign tl -->
                <div
                  class="tw:h-1 tw:bg-[#7E8093] tw:px-3 tw:mb-8 tw:mx-5 tw:hidden tw:tl:block tw:absolute tw:top-[4.8rem] tw:-left-[3.5rem]"
                />
                <!-- 風袋 -->
                <BaseLabel label="風袋" isRequired />
                <BaseInput
                  clearable
                  type="text"
                  maxlength="12"
                  inputmode="numeric"
                  autocomplete="nope"
                  :model-value="form.tareWeight"
                  @update:model-value="handleInputTareWeight"
                  :mask="{
                    mask: Number,
                    min: 0,
                    thousandsSeparator: ',',
                    scale: 2,
                    radix: '.',
                    max: 9999999.99,
                    lazy: false,
                  }"
                  outlined
                  input-class="tw:text-right tw:text-xl-design"
                  :error="!!errorStep2.tareWeight && !errorStep2.grossWeight"
                  :error-message="errorStep2.tareWeight"
                >
                  <template v-slot:append>
                    <div :class="`tw:text-[#333333] tw:pr-4 tw:tl:pr-8 tw:text-m-design tw:mt-2`">
                      g
                    </div>
                  </template>
                </BaseInput>
              </div>
            </div>
            <!-- Divider -->
            <div
              v-if="form.volumeType === VOLUME_TYPE_ENUM.WEIGHT"
              class="tw:tl:col-span-2 tw:h-[1px] tw:bg-[#CBCBCB] tw:mt-2"
              :class="{
                'tw:tl:mt-14': errorStep2.tareWeight || errorStep2.grossWeight,
                'tw:mt-14': errorStep2.tareWeight,
              }"
            />
            <div
              v-if="form.volumeType === VOLUME_TYPE_ENUM.WEIGHT"
              class="tw:tl:col-span-2 tw:flex tw:justify-between tw:items-center tw:gap-4"
            >
              <!-- 出荷量 -->
              <BaseLabel label="出荷量" />
              <div class="tw:font-bold">
                <span class="tw:text-xl-design">
                  {{ form.netWeight }}
                </span>
                <span class="tw:text-m-design"> g </span>
              </div>
            </div>
            <div v-if="form.volumeType === VOLUME_TYPE_ENUM.QUANTITY" class="tw:tl:col-span-1">
              <!-- 出荷量 -->
              <BaseLabel label="出荷量" isRequired />
              <BaseInput
                clearable
                type="text"
                maxlength="6"
                inputmode="numeric"
                autocomplete="nope"
                :model-value="form.quantity"
                @update:model-value="handleInputQuantity"
                :mask="{
                  mask: Number,
                  min: 0,
                  thousandsSeparator: ',',
                  scale: 0,
                  max: 99999,
                  lazy: false,
                }"
                outlined
                input-class="tw:text-right tw:text-xl-design"
                :error="!!errorStep2.quantity"
                :error-message="errorStep2.quantity"
              >
                <template v-slot:append>
                  <div :class="`tw:text-[#333333] tw:pr-4 tw:tl:pr-8 tw:text-m-design tw:mt-2`">
                    尾
                  </div>
                </template>
              </BaseInput>
            </div>
          </form>
        </q-tab-panel>
      </q-tab-panels>
    </div>
  </div>
  <q-footer
    elevated
    class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
    tw:tl:justify-between tw:flex tw:justify-center tw:mt-4 tw:flex-col tw:gap-4 tw:tl:flex-row"
  >
    <BaseButton
      v-if="step === STEP_ENUM.STEP_1"
      outline
      class="tw:rounded-[40px] tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]"
      label="入力内容を修正する"
      @click="handleBack"
    />
    <BaseButton
      v-else
      outline

      class="tw:rounded-[40px] tw:tl:w-[20rem] tw:bg-white tw:text-blue-3 tw:text-m-design
      tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]"
      label="前に戻る"
      @click="step = STEP_ENUM.STEP_1"
    />
    <BaseButton
      outline

      class="tw:rounded-[40px] tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:w-[24.5rem]
      tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]"
      label="次に進む"
      @click="handleClickNext"
    />
  </q-footer>
</template>
<script setup>
import dayjs from 'boot/dayjs';
import BaseButton from 'components/base/vs/BaseButton.vue';
import BaseDatePicker from 'components/base/vs/BaseDatePicker.vue';
import BaseSingleSelectInput from 'components/base/vs/BaseSingleSelectInput.vue';
import useValidate from 'composables/validate';
import { doParseFloatNumber, FORMAT_NUMBER, GENERATE_CODE_SUFFIX, isNumeric } from 'helpers/common';
import { STEP_ENUM, VOLUME_TYPE_ENUM } from 'src/helpers/constants';
import MESSAGE from 'src/helpers/message';
import { storeToRefs } from 'pinia';
import outboundShipmentService from 'services/outboundShipment.service';
import StepCustom from 'src/components/StepCustom.vue';
import BaseLabel from 'src/components/base/vs/BaseLabel.vue';
import BaseInput from 'components/base/vs/BaseInput.vue';
import {
  proxyOutboundShipmentStep1,
  proxyOutboundShipmentStep2,
} from 'src/schemas/outbound-shipment/proxyOutboundShipmentRegister.schema';
import { useAppStore } from 'stores/app-store';
import { useAuthStore } from 'stores/auth-store';
import toast from 'utilities/toast';
import { onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useConfirmFormStore } from 'src/stores/confirm-form-store';

import InputPrise from './components/InputPrise.vue';

// #region state
const router = useRouter();
const { settingUser, previousRoute } = storeToRefs(useAppStore());
const { proxyUser, user } = storeToRefs(useAuthStore());
const { setConfirmData, getConfirmData } = useConfirmFormStore();
const { signInProxyUser } = useAuthStore();
const { validateData: validateStep1, errors: errorStep1 } = useValidate();
const { validateData: validateStep2, errors: errorStep2 } = useValidate();
const unitPerGram = ref(settingUser.value.unit_per_gram ?? 0);
const INPUT_VOLUME_TYPE_OPTIONS = [
  { label: '重量', value: VOLUME_TYPE_ENUM.WEIGHT },
  { label: '尾数', value: VOLUME_TYPE_ENUM.QUANTITY },
];
const pricePerKilogramOptionsList = ref([]);
const pricePerQuantityOptionsList = ref([]);
const priceOptions = ref([]);
const form = ref({
  mainCode: '',
  subCode: '',
  supplier: '',
  volumeType: VOLUME_TYPE_ENUM.WEIGHT,
  price: '',
  date: dayjs().format('YYYY/MM/DD'),
  grossWeight: '',
  tareWeight: '',
  netWeight: '000,000.00',
  quantity: '',
});
const step = ref(STEP_ENUM.STEP_1);
const codeSuffixIdRegister = ref(null);
const supplierInfo = ref({});
// #endregion

// #region action
const handleInputGrossWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const grossWeightNum = doParseFloatNumber(newValue || 0);
  const tareWeightNum = doParseFloatNumber(form.value.tareWeight || 0);
  const netWeightNum = grossWeightNum - tareWeightNum;
  form.value.grossWeight = newValue;
  form.value.netWeight = netWeightNum >= 0 ? FORMAT_NUMBER(netWeightNum, 2) : undefined;
  form.value.quantity =
    netWeightNum >= 0
      ? FORMAT_NUMBER(Math.ceil((netWeightNum / unitPerGram.value).toFixed(3)))
      : undefined;
};

const handleInputTareWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const grossWeightNum = doParseFloatNumber(form.value.grossWeight || 0);
  const tareWeightNum = doParseFloatNumber(newValue || 0);
  const netWeightNum = grossWeightNum - tareWeightNum;
  form.value.tareWeight = newValue;
  form.value.netWeight = netWeightNum >= 0 ? FORMAT_NUMBER(netWeightNum, 2) : undefined;
  form.value.quantity =
    netWeightNum >= 0
      ? FORMAT_NUMBER(Math.ceil((netWeightNum / unitPerGram.value).toFixed(3)))
      : undefined;
};

const handleInputQuantity = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const quantityNum = doParseFloatNumber(newValue || 0);
  form.value.quantity = newValue;
  const netWeightNum = Math.ceil(quantityNum * (unitPerGram.value * 100)) / 100;
  form.value.netWeight = newValue ? FORMAT_NUMBER(netWeightNum) : undefined;
  form.value.grossWeight = form.value.netWeight;
  form.value.tareWeight = undefined;
};

const handleInputPrice = newValue => {
  const priceOptionsList =
    form.value.volumeType === VOLUME_TYPE_ENUM.WEIGHT
      ? pricePerKilogramOptionsList.value
      : pricePerQuantityOptionsList.value;

  const valueFormat = newValue.replace(/[^0-9.]/g, '')
    ? FORMAT_NUMBER(newValue.replace(/[^0-9.]/g, ''))
    : '';
  if (!valueFormat) {
    form.value.price = '';
    priceOptions.value = priceOptionsList;
    return;
  }

  const needle = valueFormat?.replaceAll(',', '') || '';
  priceOptions.value = priceOptionsList.filter(v => v.replaceAll(',', '').indexOf(needle) > -1);
};

const handleClickInputPrice = () => {
  setTimeout(() => {
    const inputPriceElement = document.querySelector('.q-dialog__inner input.q-field__native');
    if (inputPriceElement) {
      inputPriceElement.focus();
    }
  }, 200);
};

const handleBack = () => {
  signInProxyUser(null);
  router.back();
};

const handleClickNext = () => {
  if (step.value === STEP_ENUM.STEP_1) {
    const validate = validationStep1();
    if (!validate) {
      return;
    }
    step.value = STEP_ENUM.STEP_2;
  } else {
    const validate = validationStep2();
    if (!validate) {
      return;
    }
    setConfirmData({
      ...form.value,
      shipper: supplierInfo.value,
      codeSuffixId: codeSuffixIdRegister.value,
    });
    router.push({
      name: 'confirmProxyOutboundShipment',
    });
  }
};

const handleChangeVolumeType = newValue => {
  form.value.volumeType = newValue;
  if (newValue === VOLUME_TYPE_ENUM.WEIGHT) {
    priceOptions.value = pricePerKilogramOptionsList.value;
  } else {
    priceOptions.value = pricePerQuantityOptionsList.value;
  }
  form.value.price = '';
  errorStep2.value = {};
  form.value.grossWeight = '';
  form.value.tareWeight = '';
  form.value.quantity = '';
  form.value.netWeight = '000,000.00';
};
// #endregion

// #region helpers
const maskMainCode = value => {
  if (value?.length !== 13) {
    return '';
  }
  const part1 = value.slice(0, 7);
  const part2 = value.slice(7, 13);
  return `${part1}-${part2}-`;
};

const validationStep1 = () => {
  const payload = {
    date: form.value.date,
    mainCode: form.value.mainCode,
    subCode: form.value.subCode,
    supplier: form.value.supplier,
    price: form.value.price ? doParseFloatNumber(form.value.price) : undefined,
    volumeType: form.value.volumeType,
  };
  return validateStep1(proxyOutboundShipmentStep1, payload);
};

const validationStep2 = () => {
  const payload = {
    grossWeight: form.value.grossWeight ? doParseFloatNumber(form.value.grossWeight) : '',
    tareWeight: form.value.tareWeight ? doParseFloatNumber(form.value.tareWeight) : 0,
    quantity: form.value.quantity ? doParseFloatNumber(form.value.quantity) : '',
  };
  if (form.value.volumeType === VOLUME_TYPE_ENUM.WEIGHT) {
    delete payload.quantity;
  } else {
    delete payload.grossWeight;
    delete payload.tareWeight;
  }
  return validateStep2(proxyOutboundShipmentStep2, payload);
};
// #endregion

// #region watch
watch(
  () => form.value.date,
  async () => {
    if (dayjs(form.value.date, 'YYYY/MM/DD', true).isValid()) {
      form.value.mainCode =
        (proxyUser.value?.user_code?.slice(0, 7) || '') + dayjs(form.value.date).format('YYMMDD');
      const codeSuffixResponse = await GENERATE_CODE_SUFFIX(form.value.mainCode);
      codeSuffixIdRegister.value = codeSuffixResponse.id;
      form.value.subCode = codeSuffixResponse.code_suffix;
    }
  }
);
// #endregion

// #region lifecycle hooks
onMounted(async () => {
  // check if proxyUser is logged in
  // if not, redirect to login page
  if (!proxyUser.value?.id) {
    router.back();
  }

  // set options for price input
  pricePerKilogramOptionsList.value = settingUser.value.price_per_kilogram?.map(
    item => `${FORMAT_NUMBER(item)}`
  );
  pricePerQuantityOptionsList.value = settingUser.value.price_per_quantity?.map(
    item => `${FORMAT_NUMBER(item)}`
  );

  // get data from confirm form store
  const shipmentStoreData = getConfirmData();
  // if shipmentStoreData is not empty, set form values
  if (shipmentStoreData && previousRoute.value.name === 'confirmProxyOutboundShipment') {
    const { shipper, codeSuffixId, ...rest } = shipmentStoreData;
    // pass rest of the data to form
    form.value = {
      ...form.value,
      ...rest,
    };
    // set supplier and codeSuffixId
    supplierInfo.value = shipper || {};
    codeSuffixIdRegister.value = codeSuffixId;
    priceOptions.value =
      form.value.volumeType === VOLUME_TYPE_ENUM.WEIGHT
        ? pricePerKilogramOptionsList.value
        : pricePerQuantityOptionsList.value;
  } else {
    // if shipmentStoreData is empty, generate new main code and code suffix
    // check if proxyUser has permission to register outbound shipment
    // and get information about the supplier
    const checkPermissionResponse =
      await outboundShipmentService.checkPermissionForProxyOutboundShipment(proxyUser.value.id);
    if (!checkPermissionResponse.payload.hasPermission) {
      // if user does not have permission, show error message and redirect to previous page
      toast.cancel(MESSAGE.MSG_NA_CUSTOMER_ERR);
      handleBack();
    } else {
      // pass information to form
      const mainCode = (proxyUser.value?.user_code?.slice(0, 7) || '') + dayjs().format('YYMMDD');
      const codeSuffixResponse = await GENERATE_CODE_SUFFIX(mainCode);
      codeSuffixIdRegister.value = codeSuffixResponse.id;
      form.value.mainCode = mainCode;
      form.value.subCode = codeSuffixResponse.code_suffix;
      form.value.supplier = checkPermissionResponse.payload.shipper.id;
      supplierInfo.value = checkPermissionResponse.payload.shipper;

      const prePrice = checkPermissionResponse.payload.prePrice;
      if (prePrice) {
        if (prePrice.price_per_kilogram) {
          form.value.price = FORMAT_NUMBER(prePrice.price_per_kilogram);
          form.value.volumeType = VOLUME_TYPE_ENUM.WEIGHT;
          priceOptions.value = pricePerKilogramOptionsList.value;
        } else {
          form.value.price = FORMAT_NUMBER(prePrice.price_per_quantity);
          form.value.volumeType = VOLUME_TYPE_ENUM.QUANTITY;
          priceOptions.value = pricePerQuantityOptionsList.value;
        }
      } else {
        form.value.price = '';
        form.value.volumeType = VOLUME_TYPE_ENUM.WEIGHT;
        priceOptions.value = pricePerKilogramOptionsList.value;
      }
    }
  }
});
// #endregion
</script>
<style scoped>
/* Price per kilogram item - hide first child */
:deep(.price-item .q-field__control-container > div:first-child) {
  display: none;
}

/* Input price per kilogram - no background */
:deep(.input-price) {
  background: none;
}

:deep(.q-field--outlined .q-field__control) {
  padding-right: 0;
}
</style>
