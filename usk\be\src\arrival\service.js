const BaseService = require('../base/serviceFn');
const {
  LIMIT_EXPORT,
  ADMIN_SYSTEM_SETTING_KEYS_ENUM,
  ROLES_ENUM,
  ENTERPRISE_TYPE_ENUM,
  STAFF_TYPE_ENUM,
  ALLOC_STATUS,
  VOLUME_TYPE_ENUM,
  SHIPPING_INVENTORY_TYPE_ENUM,
  SHIPPING_TYPE_ENUM,
  INVENTORY_CONTROL_TYPE_ENUM,
  INVENTORY_TYPE_ENUM,
  TYPE_DIFFERENCE_WEIGHT_ENUM,
  INVENTORY_HISTORY_TYPE_ENUM,
} = require('../helpers/enum');
const { MESSAGE } = require('../utils/message');
const { ControlledException, BadRequestException } = require('../base/errors');
const dayjs = require('../boot/dayjs');

class ImportService extends BaseService {
  // ====== 1. Private method ==========
  /**
   * update inventory by edit arrival
   * @param {object} dataUpdateInventory - data update inventory
   */
  async #updateInventoryByEditArrival(tx, dataUpdateInventory) {
    const {
      arrivalNetWeight,
      arrivalGrossWeight,
      arrivalTareWeight,
      arrivalDateTime,
      dateTimeNow,
      dataTheOrigin,
      userId,
    } = dataUpdateInventory;
    const findInventory = await tx.inventories.findFirst({
      select: {
        id: true,
        group_name: true,
        net_weight_inventory: true,
        net_weight_total: true,
        gross_weight_inventory: true,
        tare_weight_inventory: true,
      },
      where: {
        id: dataTheOrigin.inventory_id,
        user_id: userId,
        delete_flag: false,
      },
    });
    let updateInfo
    if (findInventory) {
      updateInfo = {
        net_weight_inventory:
          Number(findInventory.net_weight_inventory) -
          Number(dataTheOrigin.arrival_net_weight) +
          Number(arrivalNetWeight),
        gross_weight_inventory:
          Number(findInventory.gross_weight_inventory) -
          Number(dataTheOrigin.arrival_gross_weight) +
          Number(arrivalGrossWeight),
        tare_weight_inventory:
          Number(findInventory.tare_weight_inventory) -
          Number(dataTheOrigin.arrival_tare_weight) +
          Number(arrivalTareWeight),
        net_weight_total:
          Number(findInventory.net_weight_total) -
          Number(dataTheOrigin.arrival_net_weight) +
          Number(arrivalNetWeight),
        is_history_cancel_locked: true,
        latest_updated_by_id: userId,
        latest_updated_on: dateTimeNow,
      };
      if (arrivalDateTime) updateInfo.latest_arrival_date = arrivalDateTime;
      await tx.inventories.update({
        data: { ...updateInfo },
        where: {
          id: dataTheOrigin.inventory_id,
        },
      });
    }
    return { updateInfo, findInventory };
  }

  async #checkEditOrCancelArrival(tx, theOriginInfo, user) {
    // calc can edit arrival or not
    const systemSetting = await tx.sys_settings.findFirst({
      where: {
        setting_name: ADMIN_SYSTEM_SETTING_KEYS_ENUM.EDIT_DEADLINE_FOR_ARRIVAL,
        delete_flag: false,
      },
      select: {
        setting_value: true,
      },
    });
    const calcEditArrival = dayjs(theOriginInfo.arrival_date).add(
      Number(systemSetting?.setting_value ? systemSetting.setting_value * 24 : 14 * 24),
      'hour'
    );

    // check if arrival date is before latest outbound date of the inventory
    let isArrivalBeforeLatestOutbound = false;
    if (theOriginInfo.inventory_id) {
      // find latest outbound date from the_origins where shipping_date is not null
      // and inventory is related through ingredient or shipping_id_list using raw query
      const latestOutboundResult = await tx.$queryRaw`
        SELECT shipping_date
        FROM the_origins
        WHERE shipping_date IS NOT NULL
          AND delete_flag = false
          AND (
            EXISTS (
              SELECT 1 FROM jsonb_array_elements(shipping_id_list) AS elem
              WHERE (elem->>'id')::int = ${theOriginInfo.inventory_id}
            )
            OR EXISTS (
              SELECT 1 FROM jsonb_array_elements(ingredient) AS elem
              WHERE (elem->>'shipping_inventory_id')::int = ${theOriginInfo.inventory_id}
            )
          )
        ORDER BY shipping_date DESC
        LIMIT 1
      `;

      if (latestOutboundResult && latestOutboundResult.length > 0) {
        const latestOutboundDate = latestOutboundResult[0].shipping_date;
        isArrivalBeforeLatestOutbound = dayjs(theOriginInfo.arrival_date).isBefore(
          dayjs(latestOutboundDate)
        );
      }
    }

    const canEditArrival =
      // compare arrival date (added deadline for edit arrival) with current date
      dayjs().isBefore(calcEditArrival) &&
      // check user role
      theOriginInfo.destination_user.id === user.id &&
      // check if arrival is cancelable or not
      // if cancelable_from_date is set, check if latest_updated_on is before cancelable_from_date
      (theOriginInfo.inventory.cancelable_from_date
        ? dayjs(theOriginInfo.latest_updated_on).isAfter(
          dayjs(theOriginInfo.inventory.cancelable_from_date)
        )
        : true) &&
      // check if arrival date is not before latest outbound date
      !isArrivalBeforeLatestOutbound;
    return canEditArrival;
  }

  // ====== 2. Public method ==========
  /**
   * Calculate actual arrival date
   * @param {Date} arrivalDate - arrival date
   * @returns {Object} - actual arrival date
   */
  #calcArrivalDate(arrivalDate) {
    const dateTimeNow = dayjs.getDate();
    const arrivalDateTime = dayjs.getDateFromJST(`${arrivalDate} 23:59:59`);
    return { dateTimeNow, arrivalDateTime };
  }

  /**
   * get shipping shipment detail for arrival by qr code
   * @param {object} qrCode - qrCode
   * @param {object} user - user info
   * @returns shipping shipment detail
   */
  async getQrImport(user, qrCode) {
    const connect = this.DB.READ;
    const theOriginInfo = await connect.the_origins.findFirst({
      where: {
        qr_code: qrCode,
        delete_flag: false,
      },
      select: {
        id: true,
        code: true,
        shipping_net_weight: true,
        arrival_date: true,
        starting_user_id: true,
        destination_user_id: true,
        starting_user: {
          select: {
            id: true,
            name: true,
          },
        },
        setting: true,
      },
    });
    if (!theOriginInfo) {
      throw new ControlledException(MESSAGE.MSG_INVALID_QRCODE_ERROR, {}, 402);
    }
    if (theOriginInfo.arrival_date) {
      throw new ControlledException(MESSAGE.MSG_INVALID_SHIPPINGQRCODE_ERROR, {}, 402);
    }
    if (theOriginInfo.destination_user_id !== user.id) {
      throw new ControlledException(MESSAGE.MSG_INVALID_CUSTOMER_ERR, {}, 402);
    }

    delete theOriginInfo.arrival_date;
    delete theOriginInfo.destination_user_id;
    return this.SUCCESS(theOriginInfo);
  }

  async registerArrivalManual(user, body) {
    // get connection
    const connect = this.DB.WRITE;

    // get information from body
    const {
      code,
      date,
      volume_type,
      gross_weight,
      tare_weight,
      quantity,
      supplier,
      code_suffix_id,
    } = body;

    // calculate arrival date
    const { dateTimeNow, arrivalDateTime } = this.#calcArrivalDate(date);

    // get starting user information
    const startingUser = await connect.users.findFirst({
      where: {
        id: Number(supplier),
        delete_flag: false,
      },
      select: {
        id: true,
        role: true,
        name: true,
        user_code: true,
        enterprise_id: true,
        enterprise_type: true,
        staff_type: true,
        license_number: true,
        note_1: true,
        note_2: true,
        enterprise: {
          select: {
            enterprise_name: true,
          },
        },
      },
    });
    // get destination user information
    const destinationUser = await connect.users.findFirst({
      where: {
        id: user.id,
        delete_flag: false,
      },
      select: {
        id: true,
        name: true,
        role: true,
        user_code: true,
        enterprise_id: true,
        enterprise_type: true,
        staff_type: true,
        license_number: true,
        note_1: true,
        note_2: true,
        enterprise: {
          select: {
            enterprise_name: true,
          },
        },
      },
    });

    // get user apply information
    const applyUser = await connect.users.findFirst({
      where: {
        enterprise_id: user.enterprise_id,
        staff_type: STAFF_TYPE_ENUM.ENTERPRISE,
        delete_flag: false,
      },
      select: {
        id: true,
        setting: {
          select: {
            inventory_control_type: true,
          },
        },
      },
    });
    if (!startingUser || !destinationUser || !applyUser) {
      throw new BadRequestException();
    }

    // create shipment
    const shipment = await connect.$transaction(async (tx) => {
      // handle for code suffix
      const codeSuffixBody = code.slice(-3);
      const codeSuffix = await tx.code_suffixes_alloc.findFirst({
        where: {
          id: code_suffix_id,
        },
        select: {
          code_suffix: true,
        },
      });
      // if code suffix is not exist or not match with code suffix body
      // then create a new code suffix
      if (codeSuffix && codeSuffix.code_suffix === codeSuffixBody) {
        await tx.code_suffixes_alloc.update({
          where: {
            id: code_suffix_id,
          },
          data: {
            alloc_status: ALLOC_STATUS.COMPLETED,
            latest_updated_on: dayjs().toDate(),
            latest_updated_by_id: user.id,
          },
        });
      } else {
        await tx.code_suffixes_alloc.create({
          data: {
            code_group_key: code.slice(0, 13),
            code_suffix: codeSuffixBody,
            alloc_status: ALLOC_STATUS.COMPLETED,
            created_by_id: user.id,
            created_on: dayjs().toDate(),
            latest_updated_by_id: user.id,
            latest_updated_on: dayjs().toDate(),
          },
        });
        await tx.code_suffixes_alloc.delete({
          where: {
            id: code_suffix_id,
          },
        });
      }

      // calc inventory type and shipping inventory type
      let inventoryType = INVENTORY_TYPE_ENUM.DOMESTIC;
      let shippingInventoryType = SHIPPING_INVENTORY_TYPE_ENUM.DOMESTIC;
      switch (startingUser.enterprise_type) {
        case ENTERPRISE_TYPE_ENUM.FOREIGN:
          inventoryType = INVENTORY_TYPE_ENUM.IMPORTED_EXPORTED;
          shippingInventoryType = SHIPPING_INVENTORY_TYPE_ENUM.IMPORTED_EXPORTED;
          break;
        case ENTERPRISE_TYPE_ENUM.FARM:
          inventoryType = INVENTORY_TYPE_ENUM.OTHER;
          shippingInventoryType = SHIPPING_INVENTORY_TYPE_ENUM.OTHER;
          break;
        default:
          inventoryType = INVENTORY_TYPE_ENUM.DOMESTIC;
          shippingInventoryType = SHIPPING_INVENTORY_TYPE_ENUM.DOMESTIC;
          break;
      }

      // calculate new group name
      const groupName =
        applyUser?.setting.inventory_control_type === INVENTORY_CONTROL_TYPE_ENUM.USER
          ? `${arrivalDateTime.format('MM/DD')}${startingUser.enterprise.enterprise_name}`
          : `${arrivalDateTime.format('MM/DD')}${startingUser.enterprise.enterprise_name}（${destinationUser.name
          }）`;
      // check group name of inventory is exist or not
      const checkInventoryIsExist = await tx.inventories.findFirst({
        where: {
          user_id:
            applyUser?.setting.inventory_control_type === INVENTORY_CONTROL_TYPE_ENUM.USER
              ? destinationUser.id
              : applyUser.id,
          delete_flag: false,
          group_name: groupName,
          // only check the inventory has same year as arrival date
          inventory_start_date: {
            gte: new Date(`${arrivalDateTime.year()}-01-01T00:00:00.000Z`),
            lt: new Date(`${arrivalDateTime.year() + 1}-01-01T00:00:00.000Z`),
          },
          inventory_type: inventoryType,
        },
      });

      // handle logic with the_origins
      const netWeight = gross_weight - tare_weight;
      const newShipment = await tx.the_origins.create({
        data: {
          // TODO: validation code
          code: code,
          shipping_date: arrivalDateTime.toDate(),
          shipping_gross_weight: gross_weight,
          shipping_tare_weight: tare_weight,
          shipping_net_weight: netWeight,
          shipping_quantity: volume_type === VOLUME_TYPE_ENUM.QUANTITY ? quantity : 0,
          arrival_gross_weight: gross_weight,
          arrival_tare_weight: tare_weight,
          arrival_net_weight: netWeight,
          arrival_quantity: volume_type === VOLUME_TYPE_ENUM.QUANTITY ? quantity : 0,
          arrival_date: arrivalDateTime.toDate(),
          shipping_type: SHIPPING_TYPE_ENUM.ARRIVAL_MANUAL,
          created_by_id: user.id,
          latest_updated_by_id: user.id,
          created_on: dateTimeNow.toDate(),
          latest_updated_on: dateTimeNow.toDate(),
          destination_enterprise_id: destinationUser.enterprise_id,
          starting_enterprise_id: startingUser.enterprise_id,
          destination_user_id: destinationUser.id,
          starting_user_id: startingUser.id,
          destination_enterprise_name: destinationUser.enterprise.enterprise_name,
          destination_user_name: destinationUser.name,
          destination_license_number: destinationUser.license_number,
          destination_user_note_1: destinationUser.note_1,
          destination_user_note_2: destinationUser.note_2,
          starting_enterprise_name: startingUser.enterprise.enterprise_name,
          starting_user_name: startingUser.name,
          starting_license_number: startingUser.license_number,
          starting_user_note_1: startingUser.note_1,
          starting_user_note_2: startingUser.note_2,
          shipping_inventory_type: shippingInventoryType,
        },
      });

      // handle for inventory
      let inventoryId = null;
      // if inventory is not exist
      if (!checkInventoryIsExist) {
        // create inventory
        const newInventory = await tx.inventories.create({
          data: {
            group_name: groupName,
            gross_weight_inventory: gross_weight,
            tare_weight_inventory: tare_weight,
            net_weight_inventory: netWeight,
            net_weight_total: netWeight,
            latest_arrival_date: dateTimeNow.toDate(),
            is_history_cancel_locked: true,
            created_by_id: user.id,
            latest_updated_by_id: user.id,
            created_on: dateTimeNow.toDate(),
            latest_updated_on: dateTimeNow.toDate(),
            user_id:
              applyUser?.setting.inventory_control_type === INVENTORY_CONTROL_TYPE_ENUM.USER
                ? user.id
                : applyUser.id,
            current_arrival_id_list: [
              {
                id: newShipment.id,
                code: newShipment.code,
                starting_enterprise_name: startingUser.enterprise.enterprise_name,
                starting_user_name: startingUser.name,
              },
            ],
            inventory_type: inventoryType,
            inventory_start_date: dateTimeNow,
          },
        });
        inventoryId = newInventory.id;
      }
      // if inventory is exist
      else {
        inventoryId = checkInventoryIsExist.id;
        // create inventory history
        await tx.inventories_history.create({
          data: {
            inventory_id: inventoryId,
            group_name: groupName,
            gross_weight_inventory: checkInventoryIsExist.gross_weight_inventory,
            tare_weight_inventory: checkInventoryIsExist.tare_weight_inventory,
            net_weight_inventory: checkInventoryIsExist.net_weight_total,
            new_gross_weight_inventory:
              checkInventoryIsExist.gross_weight_inventory?.toNumber() + gross_weight,
            new_tare_weight_inventory:
              checkInventoryIsExist.tare_weight_inventory?.toNumber() + tare_weight,
            new_net_weight_inventory:
              checkInventoryIsExist.net_weight_total?.toNumber() + netWeight,
            net_weight_total: checkInventoryIsExist.net_weight_total,
            latest_arrival_date: dateTimeNow.toDate(),
            created_by_id: user.id,
            latest_updated_by_id: user.id,
            latest_updated_on: dateTimeNow.toDate(),
            created_on: dateTimeNow.toDate(),
            user_id: destinationUser.id,
            type_diff: TYPE_DIFFERENCE_WEIGHT_ENUM.BY_SYSTEM,
            reason_diff: '',
            current_arrival_id_list: checkInventoryIsExist.current_arrival_id_list,
            new_current_arrival_id_list: [
              ...checkInventoryIsExist.current_arrival_id_list,
              {
                id: newShipment.id,
                code: newShipment.code,
                starting_enterprise_name: startingUser.enterprise.enterprise_name,
                starting_user_name: startingUser.name,
              },
            ],
            is_display_inventories_history: true,
          },
        });

        // update inventory
        await tx.inventories.update({
          where: {
            id: inventoryId,
          },
          data: {
            gross_weight_inventory: {
              increment: gross_weight,
            },
            tare_weight_inventory: {
              increment: tare_weight,
            },
            net_weight_inventory: {
              increment: netWeight,
            },
            net_weight_total: {
              increment: netWeight,
            },
            latest_arrival_date: dateTimeNow.toDate(),
            latest_updated_by_id: user.id,
            latest_updated_on: dateTimeNow.toDate(),
            current_arrival_id_list: [
              ...checkInventoryIsExist.current_arrival_id_list,
              {
                id: newShipment.id,
                code: newShipment.code,
                starting_enterprise_name: startingUser.enterprise.enterprise_name,
                starting_user_name: startingUser.name,
              },
            ],
            is_history_cancel_locked: true,
          },
        });
      }

      // update shipment with inventory id
      await tx.the_origins.update({
        where: {
          id: newShipment.id,
        },
        data: {
          inventory_id: inventoryId,
        },
      });

      return newShipment;
    });

    // get the shipment result
    const shipmentResult = await connect.the_origins.findFirst({
      where: {
        id: shipment.id,
      },
      select: {
        code: true,
        shipping_date: true,
        shipping_gross_weight: true,
        shipping_tare_weight: true,
        shipping_net_weight: true,
        shipping_quantity: true,
        arrival_date: true,
        arrival_gross_weight: true,
        arrival_tare_weight: true,
        arrival_net_weight: true,
        qr_code: true,
        setting: true,
      },
    });

    return this.SUCCESS(shipmentResult);
  }

  /**
   * register arrival normal
   * @param {object} body - data body request
   * @param {object} user - user info
   */
  async registerArrivalByQr(user, body) {
    // get connection
    const connect = this.DB.WRITE;

    // get information from body
    const { code, date, gross_weight, tare_weight, quantity, supplier, type_diff, reason_diff } =
      body;

    // calculate arrival date
    const { dateTimeNow, arrivalDateTime } = this.#calcArrivalDate(date);

    // get starting user information
    const startingUser = await connect.users.findFirst({
      where: {
        id: Number(supplier),
        delete_flag: false,
      },
      select: {
        id: true,
        role: true,
        name: true,
        user_code: true,
        enterprise_id: true,
        enterprise_type: true,
        staff_type: true,
        license_number: true,
        note_1: true,
        note_2: true,
        enterprise: {
          select: {
            enterprise_name: true,
          },
        },
      },
    });
    // get destination user information
    const destinationUser = await connect.users.findFirst({
      where: {
        id: user.id,
        delete_flag: false,
      },
      select: {
        id: true,
        name: true,
        role: true,
        user_code: true,
        enterprise_id: true,
        enterprise_type: true,
        staff_type: true,
        license_number: true,
        note_1: true,
        note_2: true,
        enterprise: {
          select: {
            enterprise_name: true,
          },
        },
      },
    });
    // get user apply information
    const applyUser = await connect.users.findFirst({
      where: {
        enterprise_id: user.enterprise_id,
        staff_type: STAFF_TYPE_ENUM.ENTERPRISE,
        delete_flag: false,
      },
      select: {
        id: true,
        setting: {
          select: {
            inventory_control_type: true,
          },
        },
      },
    });
    // find shipping shipment
    const shipment = await connect.the_origins.findFirst({
      where: {
        code,
        delete_flag: false,
      },
    });

    if (!applyUser || !startingUser || !destinationUser || !shipment) {
      throw new BadRequestException();
    }

    // calculate actual shipping date
    const shippingDateTime = dayjs.getDate(shipment.shipping_date);

    // calc net weight
    const netWeight = gross_weight - tare_weight;

    // update shipment
    const updatedShipment = await connect.$transaction(async (tx) => {
      // calculate new group name
      const groupName =
        applyUser?.setting.inventory_control_type === INVENTORY_CONTROL_TYPE_ENUM.USER
          ? `${shippingDateTime.format('MM/DD')}${startingUser.enterprise.enterprise_name}`
          : `${shippingDateTime.format('MM/DD')}${startingUser.enterprise.enterprise_name}（${destinationUser.name
          }）`;
      // check group name of inventory is exist or not
      const checkInventoryIsExist = await tx.inventories.findFirst({
        where: {
          user_id:
            applyUser?.setting.inventory_control_type === INVENTORY_CONTROL_TYPE_ENUM.USER
              ? destinationUser.id
              : applyUser.id,
          delete_flag: false,
          group_name: groupName,
          // only check the inventory has same year as arrival date
          inventory_start_date: {
            gte: new Date(`${shippingDateTime.year()}-01-01T00:00:00.000Z`),
            lt: new Date(`${shippingDateTime.year() + 1}-01-01T00:00:00.000Z`),
          },
          inventory_type: shipment.shipping_inventory_type,
        },
      });

      // handle for inventory
      let inventoryId = null;
      // if inventory is not exist
      if (!checkInventoryIsExist) {
        // create inventory
        const newInventory = await tx.inventories.create({
          data: {
            group_name: groupName,
            gross_weight_inventory: gross_weight,
            tare_weight_inventory: tare_weight,
            net_weight_inventory: netWeight,
            net_weight_total: netWeight,
            latest_arrival_date: dateTimeNow.toDate(),
            is_history_cancel_locked: true,
            created_by_id: user.id,
            latest_updated_by_id: user.id,
            created_on: dateTimeNow.toDate(),
            latest_updated_on: dateTimeNow.toDate(),
            user_id:
              applyUser?.setting.inventory_control_type === INVENTORY_CONTROL_TYPE_ENUM.USER
                ? user.id
                : applyUser.id,
            current_arrival_id_list: [
              {
                id: shipment.id,
                code: shipment.code,
                starting_enterprise_name: startingUser.enterprise.enterprise_name,
                starting_user_name: startingUser.name,
              },
            ],
            inventory_type: shipment.shipping_inventory_type,
            inventory_start_date: dateTimeNow,
          },
        });
        inventoryId = newInventory.id;
      }
      // if inventory is exist
      else {
        inventoryId = checkInventoryIsExist.id;
        // create inventory history
        await tx.inventories_history.create({
          data: {
            inventory_id: inventoryId,
            group_name: groupName,
            gross_weight_inventory: checkInventoryIsExist.gross_weight_inventory,
            tare_weight_inventory: checkInventoryIsExist.tare_weight_inventory,
            net_weight_inventory: checkInventoryIsExist.net_weight_total,
            new_gross_weight_inventory:
              checkInventoryIsExist.gross_weight_inventory?.toNumber() + gross_weight,
            new_tare_weight_inventory:
              checkInventoryIsExist.tare_weight_inventory?.toNumber() + tare_weight,
            new_net_weight_inventory:
              checkInventoryIsExist.net_weight_total?.toNumber() + netWeight,
            net_weight_total: checkInventoryIsExist.net_weight_total,
            latest_arrival_date: dateTimeNow.toDate(),
            created_by_id: user.id,
            latest_updated_by_id: user.id,
            latest_updated_on: dateTimeNow.toDate(),
            created_on: dateTimeNow.toDate(),
            user_id: destinationUser.id,
            type_diff: TYPE_DIFFERENCE_WEIGHT_ENUM.BY_SYSTEM,
            reason_diff: '',
            current_arrival_id_list: checkInventoryIsExist.current_arrival_id_list,
            new_current_arrival_id_list: [
              ...checkInventoryIsExist.current_arrival_id_list,
              {
                id: shipment.id,
                code: shipment.code,
                starting_enterprise_name: startingUser.enterprise.enterprise_name,
                starting_user_name: startingUser.name,
              },
            ],
            is_display_inventories_history: true,
          },
        });

        // update inventory
        await tx.inventories.update({
          where: {
            id: inventoryId,
          },
          data: {
            gross_weight_inventory: {
              increment: gross_weight,
            },
            tare_weight_inventory: {
              increment: tare_weight,
            },
            net_weight_inventory: {
              increment: netWeight,
            },
            net_weight_total: {
              increment: netWeight,
            },
            latest_arrival_date: dateTimeNow.toDate(),
            latest_updated_by_id: user.id,
            latest_updated_on: dateTimeNow.toDate(),
            current_arrival_id_list: [
              ...checkInventoryIsExist.current_arrival_id_list,
              {
                id: shipment.id,
                code: shipment.code,
                starting_enterprise_name: startingUser.enterprise.enterprise_name,
                starting_user_name: startingUser.name,
              },
            ],
            is_history_cancel_locked: true,
          },
        });
      }

      // handle logic for the_origins_history
      await connect.the_origins_history.create({
        data: {
          the_origins_history_type: INVENTORY_HISTORY_TYPE_ENUM.UPDATE,
          the_origins_id: shipment.id,
          the_origins_history_created_by_id: user.id,
          the_origins_history_created_on: dateTimeNow.toDate(),
          code: shipment.code,
          shipping_date: shipment.shipping_date,
          shipping_gross_weight: shipment.shipping_gross_weight,
          shipping_tare_weight: shipment.shipping_tare_weight,
          shipping_net_weight: shipment.shipping_net_weight,
          shipping_quantity: shipment.shipping_quantity,
          arrival_gross_weight: shipment.arrival_gross_weight,
          arrival_tare_weight: shipment.arrival_tare_weight,
          arrival_net_weight: shipment.arrival_net_weight,
          arrival_quantity: shipment.arrival_quantity,
          arrival_date: shipment.arrival_date,
          type_diff: shipment.type_diff,
          shipping_type: shipment.shipping_type,
          reason_diff: shipment.reason_diff,
          created_by_id: shipment.created_by_id,
          latest_updated_by_id: shipment.latest_updated_by_id,
          created_on: shipment.created_on,
          latest_updated_on: shipment.latest_updated_on,
          delete_flag: shipment.delete_flag,
          qr_code: shipment.qr_code,
          destination_enterprise_id: shipment.destination_enterprise_id,
          starting_enterprise_id: shipment.starting_enterprise_id,
          inventory_id: shipment.inventory_id,
          ingredient: shipment.ingredient,
          setting: shipment.setting,
          destination_user_id: shipment.destination_user_id,
          starting_user_id: shipment.starting_user_id,
          shipping_type_diff: shipment.shipping_type_diff,
          shipping_reason_diff: shipment.shipping_reason_diff,
          destination_enterprise_name: shipment.destination_enterprise_name,
          destination_user_name: shipment.destination_user_name,
          destination_license_number: shipment.destination_license_number,
          destination_user_note_1: shipment.destination_user_note_1,
          destination_user_note_2: shipment.destination_user_note_2,
          starting_enterprise_name: shipment.starting_enterprise_name,
          starting_user_name: shipment.starting_user_name,
          starting_license_number: shipment.starting_license_number,
          starting_user_note_1: shipment.starting_user_note_1,
          starting_user_note_2: shipment.starting_user_note_2,
          shipping_inventory_type: shipment.shipping_inventory_type,
          shipping_id_list: shipment.shipping_id_list,
        },
      });
      // handle logic with the_origins
      return await connect.the_origins.update({
        where: {
          id: shipment.id,
          code,
          delete_flag: false,
        },
        data: {
          arrival_gross_weight: gross_weight,
          arrival_tare_weight: tare_weight,
          arrival_net_weight: netWeight,
          arrival_quantity: quantity,
          arrival_date: arrivalDateTime.toDate(),
          type_diff,
          reason_diff,
          latest_updated_by_id: user.id,
          latest_updated_on: dateTimeNow.toDate(),
          destination_enterprise_id: user.enterprise_id,
          inventory_id: inventoryId,
        },
      });
    });

    // get the shipment result
    const shipmentResult = await connect.the_origins.findFirst({
      where: {
        id: updatedShipment.id,
      },
      select: {
        code: true,
        shipping_date: true,
        shipping_gross_weight: true,
        shipping_tare_weight: true,
        shipping_net_weight: true,
        shipping_quantity: true,
        arrival_date: true,
        arrival_gross_weight: true,
        arrival_tare_weight: true,
        arrival_net_weight: true,
        qr_code: true,
        setting: true,
      },
    });

    return this.SUCCESS(shipmentResult);
  }

  /**
   * get list arrival
   * @param {object} query - data query request
   * @param {object} user - user auth
   */
  async getListArrival(query, user) {
    const {
      supplier,
      startArrivalDate,
      endArrivalDate,
      licenseNumber,
      note1,
      note2,
      page,
      limit,
      enterpriseName,
      enterpriseCode,
    } = query;
    const connect = this.DB.READ;
    const searchCondition = {
      delete_flag: false,
      destination_enterprise_id: user.enterprise_id,
      starting_license_number: licenseNumber
        ? {
          contains: licenseNumber.replace(/\s+/g, ''),
        }
        : undefined,
      starting_user_note_1: note1
        ? {
          contains: note1.replace(/\s+/g, ''),
        }
        : undefined,
      starting_user_note_2: note2
        ? {
          contains: note2.replace(/\s+/g, ''),
        }
        : undefined,
      starting_user_name: supplier
        ? {
          contains: supplier.replace(/\s+/g, ''),
        }
        : undefined,
      starting_enterprise_name: enterpriseName
        ? {
          contains: enterpriseName.replace(/\s+/g, ''),
        }
        : undefined,
      starting_enterprise: {
        enterprise_code: enterpriseCode
          ? {
            contains: enterpriseCode.replace(/\s+/g, ''),
          }
          : undefined,
      },
      inventory_id: {
        not: null,
      },
      arrival_date: {
        gte: startArrivalDate
          ? dayjs.getDateFromJST(`${startArrivalDate} 00:00:00`).toDate()
          : undefined,
        lte: endArrivalDate
          ? dayjs.getDateFromJST(`${endArrivalDate} 23:59:59`).toDate()
          : undefined,
      },
      arrival_net_weight: {
        gt: 0,
      },
      destination_user_id:
        user.role === ROLES_ENUM.NORMAL_USER &&
          user.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE &&
          user.staff_type === STAFF_TYPE_ENUM.STAFF
          ? user.id
          : undefined,
    };

    const filter = {
      select: {
        id: true,
        arrival_date: true,
        code: true,
        arrival_net_weight: true,
        reason_diff: true,
        type_diff: true,
        destination_enterprise_name: true,
        starting_enterprise_name: true,
        starting_user_name: true,
        destination_user_name: true,
        starting_license_number: true,
        destination_license_number: true,
        starting_enterprise: {
          select: {
            enterprise_code: true,
          },
        },
        destination_enterprise: {
          select: {
            enterprise_name: true,
          },
        },
        starting_user: {
          select: {
            name: true,
            role: true,
            license_number: true,
            enterprise_type: true,
            staff_type: true,
          },
        },
        shipping_id_list: true,
        destination_user: {
          select: {
            name: true,
            role: true,
            license_number: true,
            enterprise_type: true,
            staff_type: true,
          },
        },
      },
      where: searchCondition,
    };
    const totalCount = await connect.the_origins.count({
      where: searchCondition,
    });

    if (totalCount === 0) {
      return this.SUCCESS({
        items: [],
        total_item: 0,
        page: 1,
        page_size: limit,
      });
    }

    let tempPage = page;
    if (totalCount > LIMIT_EXPORT) {
      throw new ControlledException(MESSAGE.MSG_LIMIT_EXPORT);
    }
    const data = await connect.the_origins.findMany({
      ...filter,
      orderBy: { arrival_date: 'desc' }
    });
    return this.SUCCESS({
      items: data,
      total_item: totalCount,
      page: tempPage,
      page_size: +limit,
    });
  }

  /**
   * get detail arrival
   * @param {object} arrivalId - id of arrival
   * @param {object} user - user auth
   */
  async getArrivalDetail(arrivalId, user) {
    const connect = this.DB.READ;
    const data = await connect.the_origins.findFirst({
      where: {
        delete_flag: false,
        id: +arrivalId,
        destination_enterprise_id: user.enterprise_id,
        arrival_net_weight: {
          gt: 0,
        },
      },
      select: {
        id: true,
        code: true,
        arrival_net_weight: true,
        arrival_quantity: true,
        arrival_gross_weight: true,
        arrival_tare_weight: true,
        shipping_date: true,
        arrival_date: true,
        qr_code: true,
        shipping_net_weight: true,
        setting: true,
        latest_updated_on: true,
        shipping_type: true,
        reason_diff: true,
        type_diff: true,
        starting_enterprise_name: true,
        starting_user_name: true,
        starting_license_number: true,
        starting_user_note_1: true,
        starting_user_note_2: true,
        destination_enterprise_name: true,
        destination_user_name: true,
        destination_license_number: true,
        destination_user_note_1: true,
        destination_user_note_2: true,
        starting_user: {
          select: {
            note_1: true,
            note_2: true,
            license_number: true,
            name: true,
            role: true,
            enterprise_type: true,
            staff_type: true,
          },
        },
        destination_enterprise: {
          select: {
            enterprise_name: true,
          },
        },
        starting_enterprise: {
          select: {
            enterprise_name: true,
          },
        },
        inventory: {
          select: {
            cancelable_from_date: true,
            is_history_cancel_locked: true,
          },
        },
        destination_user: {
          select: {
            id: true,
            name: true,
            role: true,
            enterprise_type: true,
            staff_type: true,
          },
        },
      },
    });
    if (!data) {
      throw new ControlledException(MESSAGE.ID_ARRIVAL_NOT_FOUND, {}, 402);
    }

    // calc can edit arrival or not
    const canEditArrival = await this.#checkEditOrCancelArrival(connect, data, user);

    return this.SUCCESS({
      ...data,
      can_edit: canEditArrival,
    });
  }

  /**
   * edit detail arrival
   * @param {object} user - user auth
   * @param {object} arrivalId - id of arrival
   * @param {object} dataBody - data body request
   */
  async editArrival(user, arrivalId, dataBody) {
    const { arrivalDate, arrivalGrossWeight, arrivalQuantity, reasonDiff, typeDiff } = dataBody;
    const arrivalTareWeight = dataBody.arrivalTareWeight ?? 0;
    const { id: userId, enterprise_id: enterpriseId } = user;
    const arrivalNetWeight = arrivalGrossWeight - arrivalTareWeight;
    const connect = this.DB.WRITE;
    const { dateTimeNow, arrivalDateTime } = this.#calcArrivalDate(arrivalDate);
    const dataTheOrigin = await connect.the_origins.findFirst({
      where: {
        delete_flag: false,
        id: +arrivalId,
        destination_enterprise_id: enterpriseId,
      },
      include: {
        inventory: {
          select: {
            cancelable_from_date: true,
          },
        },
        starting_user: {
          select: {
            id: true,
          },
        },
        destination_user: {
          select: {
            id: true,
          },
        },
      },
    });
    if (!dataTheOrigin || !dataTheOrigin.inventory_id) {
      throw new ControlledException(MESSAGE.ID_ARRIVAL_NOT_FOUND);
    }

    // check can edit arrival or not
    const checkEditOrCancelArrival = await this.#checkEditOrCancelArrival(
      connect,
      dataTheOrigin,
      user
    );
    if (!checkEditOrCancelArrival) {
      // TODO: throw error message with japanese
      throw new ControlledException('Can not edit arrival');
    }

    await connect.$transaction(async (tx) => {
      // update inventory
      const dataUpdateInventory = {
        arrivalNetWeight,
        arrivalGrossWeight,
        arrivalTareWeight,
        arrivalQuantity,
        arrivalDateTime: arrivalDateTime.toDate(),
        dateTimeNow: dateTimeNow.toDate(),
        dataTheOrigin,
        userId,
        reasonDiff,
        typeDiff,
      };
      await tx.the_origins_history.create({
        data: {
          the_origins_history_type: INVENTORY_HISTORY_TYPE_ENUM.UPDATE,
          the_origins_id: dataTheOrigin.id,
          the_origins_history_created_by_id: user.id,
          the_origins_history_created_on: dateTimeNow.toDate(),
          code: dataTheOrigin.code,
          shipping_date: dataTheOrigin.shipping_date,
          shipping_gross_weight: dataTheOrigin.shipping_gross_weight,
          shipping_tare_weight: dataTheOrigin.shipping_tare_weight,
          shipping_net_weight: dataTheOrigin.shipping_net_weight,
          shipping_quantity: dataTheOrigin.shipping_quantity,
          arrival_gross_weight: dataTheOrigin.arrival_gross_weight,
          arrival_tare_weight: dataTheOrigin.arrival_tare_weight,
          arrival_net_weight: dataTheOrigin.arrival_net_weight,
          arrival_quantity: dataTheOrigin.arrival_quantity,
          arrival_date: dataTheOrigin.arrival_date,
          type_diff: dataTheOrigin.type_diff,
          shipping_type: dataTheOrigin.shipping_type,
          reason_diff: dataTheOrigin.reason_diff,
          created_by_id: dataTheOrigin.created_by_id,
          latest_updated_by_id: dataTheOrigin.latest_updated_by_id,
          created_on: dataTheOrigin.created_on,
          latest_updated_on: dataTheOrigin.latest_updated_on,
          delete_flag: dataTheOrigin.delete_flag,
          qr_code: dataTheOrigin.qr_code,
          destination_enterprise_id: dataTheOrigin.destination_enterprise_id,
          starting_enterprise_id: dataTheOrigin.starting_enterprise_id,
          inventory_id: dataTheOrigin.inventory_id,
          ingredient: dataTheOrigin.ingredient,
          setting: dataTheOrigin.setting,
          destination_user_id: dataTheOrigin.destination_user_id,
          starting_user_id: dataTheOrigin.starting_user_id,
          shipping_type_diff: dataTheOrigin.shipping_type_diff,
          shipping_reason_diff: dataTheOrigin.shipping_reason_diff,
          destination_enterprise_name: dataTheOrigin.destination_enterprise_name,
          destination_user_name: dataTheOrigin.destination_user_name,
          destination_license_number: dataTheOrigin.destination_license_number,
          destination_user_note_1: dataTheOrigin.destination_user_note_1,
          destination_user_note_2: dataTheOrigin.destination_user_note_2,
          starting_enterprise_name: dataTheOrigin.starting_enterprise_name,
          starting_user_name: dataTheOrigin.starting_user_name,
          starting_license_number: dataTheOrigin.starting_license_number,
          starting_user_note_1: dataTheOrigin.starting_user_note_1,
          starting_user_note_2: dataTheOrigin.starting_user_note_2,
          shipping_inventory_type: dataTheOrigin.shipping_inventory_type,
          shipping_id_list: dataTheOrigin.shipping_id_list,
        },
      });
      const { updateInfo, findInventory } = await this.#updateInventoryByEditArrival(tx, dataUpdateInventory);
      if (findInventory) {
        tx.inventories_history.create({
          data: {
            inventory_id: findInventory.id,
            group_name: findInventory.group_name,
            gross_weight_inventory: findInventory.gross_weight_inventory,
            new_tare_weight_inventory: updateInfo.tare_weight_inventory,
            net_weight_inventory: findInventory.net_weight_inventory,
            new_gross_weight_inventory: updateInfo.gross_weight_inventory,
            tare_weight_inventory: findInventory.tare_weight_inventory,
            new_net_weight_inventory: updateInfo.net_weight_inventory,
            net_weight_total: findInventory.net_weight_total,
            latest_arrival_date: dateTimeNow.toDate(),
            created_by_id: user.id,
            latest_updated_by_id: user.id,
            created_on: dateTimeNow.toDate(),
            latest_updated_on: dateTimeNow.toDate(),
            user_id: findInventory.user_id,
            type_diff: INVENTORY_HISTORY_TYPE_ENUM.UPDATE,
            current_arrival_id_list: findInventory.current_arrival_id_list,
            new_current_arrival_id_list: findInventory.current_arrival_id_list,
            is_display_inventories_history: false,
          }
        })
      }

      // update arrival
      const dataUpdateTheOrigins = {
        arrival_gross_weight: arrivalGrossWeight,
        arrival_tare_weight: arrivalTareWeight,
        arrival_net_weight: arrivalNetWeight,
        arrival_quantity: arrivalQuantity,
        arrival_date: arrivalDateTime.toDate(),
        latest_updated_by_id: userId,
        latest_updated_on: dateTimeNow.toDate(),
        type_diff: typeDiff,
        reason_diff: reasonDiff,
      };
      await tx.the_origins.update({
        data: dataTheOrigin.qr_code
          ? {
            ...dataUpdateTheOrigins,
          }
          : {
            ...dataUpdateTheOrigins,
            shipping_gross_weight: arrivalNetWeight,
            shipping_tare_weight: 0,
            shipping_net_weight: arrivalNetWeight,
            shipping_quantity: arrivalQuantity,
            shipping_date: arrivalDateTime.toDate(),
            type_diff: typeDiff,
            reason_diff: reasonDiff,
          },
        where: {
          id: dataTheOrigin.id,
        },
      });
    });
    return this.SUCCESS({});
  }

  /**
   * cancel detail arrival
   * @param {object} user - user auth
   * @param {object} arrivalId - id of arrival
   */
  async cancelArrival(user, arrivalId) {
    const { id: userId, enterprise_id: enterpriseId } = user;
    const connect = this.DB.WRITE;
    const dateTimeNow = dayjs().toDate();
    const dataTheOrigin = await connect.the_origins.findFirst({
      where: {
        delete_flag: false,
        id: +arrivalId,
        starting_enterprise_id: enterpriseId,
      },
      include: {
        inventory: {
          select: {
            cancelable_from_date: true,
          },
        },
        starting_user: {
          select: {
            id: true,
          },
        },
        destination_user: {
          select: {
            id: true,
          },
        },
      },
    });
    if (!dataTheOrigin || !dataTheOrigin.inventory_id) {
      throw new ControlledException(MESSAGE.ID_ARRIVAL_NOT_FOUND);
    }

    // check can edit arrival or not
    const checkEditOrCancelArrival = await this.#checkEditOrCancelArrival(
      connect,
      dataTheOrigin,
      user
    );
    if (!checkEditOrCancelArrival) {
      // TODO: throw error message with japanese
      throw new ControlledException('Can not cancel arrival');
    }

    await connect.$transaction(async (tx) => {
      // update inventory
      const dataUpdateInventory = {
        arrivalNetWeight: 0,
        arrivalGrossWeight: 0,
        arrivalTareWeight: 0,
        arrivalQuantity: 0,
        dateTimeNow,
        enterpriseId,
        dataTheOrigin,
        userId,
      };
      const { updateInfo, findInventory } = await this.#updateInventoryByEditArrival(tx, dataUpdateInventory);
      if (findInventory) {
        tx.inventories_history.create({
          data: {
            inventory_id: findInventory.id,
            group_name: findInventory.group_name,
            gross_weight_inventory: findInventory.gross_weight_inventory,
            new_tare_weight_inventory: updateInfo.tare_weight_inventory,
            net_weight_inventory: findInventory.net_weight_inventory,
            new_gross_weight_inventory: updateInfo.gross_weight_inventory,
            tare_weight_inventory: findInventory.tare_weight_inventory,
            new_net_weight_inventory: updateInfo.net_weight_inventory,
            net_weight_total: findInventory.net_weight_total,
            latest_arrival_date: dateTimeNow,
            created_by_id: user.id,
            latest_updated_by_id: user.id,
            created_on: dateTimeNow,
            latest_updated_on: dateTimeNow,
            user_id: findInventory.user_id,
            type_diff: INVENTORY_HISTORY_TYPE_ENUM.UPDATE,
            current_arrival_id_list: findInventory.current_arrival_id_list,
            new_current_arrival_id_list: findInventory.current_arrival_id_list,
            is_display_inventories_history: false,
          }
        });
      }
      await tx.the_origins_history.create({
        data: {
          the_origins_history_type: INVENTORY_HISTORY_TYPE_ENUM.UPDATE,
          the_origins_id: dataTheOrigin.id,
          the_origins_history_created_by_id: user.id,
          the_origins_history_created_on: dateTimeNow,
          code: dataTheOrigin.code,
          shipping_date: dataTheOrigin.shipping_date,
          shipping_gross_weight: dataTheOrigin.shipping_gross_weight,
          shipping_tare_weight: dataTheOrigin.shipping_tare_weight,
          shipping_net_weight: dataTheOrigin.shipping_net_weight,
          shipping_quantity: dataTheOrigin.shipping_quantity,
          arrival_gross_weight: dataTheOrigin.arrival_gross_weight,
          arrival_tare_weight: dataTheOrigin.arrival_tare_weight,
          arrival_net_weight: dataTheOrigin.arrival_net_weight,
          arrival_quantity: dataTheOrigin.arrival_quantity,
          arrival_date: dataTheOrigin.arrival_date,
          type_diff: dataTheOrigin.type_diff,
          shipping_type: dataTheOrigin.shipping_type,
          reason_diff: dataTheOrigin.reason_diff,
          created_by_id: dataTheOrigin.created_by_id,
          latest_updated_by_id: dataTheOrigin.latest_updated_by_id,
          created_on: dataTheOrigin.created_on,
          latest_updated_on: dataTheOrigin.latest_updated_on,
          delete_flag: dataTheOrigin.delete_flag,
          qr_code: dataTheOrigin.qr_code,
          destination_enterprise_id: dataTheOrigin.destination_enterprise_id,
          starting_enterprise_id: dataTheOrigin.starting_enterprise_id,
          inventory_id: dataTheOrigin.inventory_id,
          ingredient: dataTheOrigin.ingredient,
          setting: dataTheOrigin.setting,
          destination_user_id: dataTheOrigin.destination_user_id,
          starting_user_id: dataTheOrigin.starting_user_id,
          shipping_type_diff: dataTheOrigin.shipping_type_diff,
          shipping_reason_diff: dataTheOrigin.shipping_reason_diff,
          destination_enterprise_name: dataTheOrigin.destination_enterprise_name,
          destination_user_name: dataTheOrigin.destination_user_name,
          destination_license_number: dataTheOrigin.destination_license_number,
          destination_user_note_1: dataTheOrigin.destination_user_note_1,
          destination_user_note_2: dataTheOrigin.destination_user_note_2,
          starting_enterprise_name: dataTheOrigin.starting_enterprise_name,
          starting_user_name: dataTheOrigin.starting_user_name,
          starting_license_number: dataTheOrigin.starting_license_number,
          starting_user_note_1: dataTheOrigin.starting_user_note_1,
          starting_user_note_2: dataTheOrigin.starting_user_note_2,
          shipping_inventory_type: dataTheOrigin.shipping_inventory_type,
          shipping_id_list: dataTheOrigin.shipping_id_list,
        },
      });
      // update arrival
      const dataUpdateTheOrigins = {
        arrival_gross_weight: 0,
        arrival_tare_weight: 0,
        arrival_net_weight: 0,
        arrival_quantity: 0,
        arrival_date: null,
        type_diff: null,
        reason_diff: null,
        latest_updated_by_id: userId,
        latest_updated_on: dateTimeNow,
      };
      // Delete records when canceling arrival in case of alternate shipping and manual arrival
      if (!dataTheOrigin.qr_code) {
        dataUpdateTheOrigins.delete_flag = true;
      }
      await tx.the_origins.update({
        data: {
          ...dataUpdateTheOrigins,
        },
        where: {
          id: dataTheOrigin.id,
        },
      });
    });
    return this.SUCCESS({});
  }
}

module.exports = ImportService;
