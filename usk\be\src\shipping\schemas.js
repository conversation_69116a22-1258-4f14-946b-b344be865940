const { sharedSchema } = require('../validations');
const { MESSAGE } = require('../utils/message');
const { TYPE_DIFFERENCE_WEIGHT_ENUM } = require('../helpers/enum');

const getShippingListSchema = {
  summary: 'Get shipping list',
  description: 'Get shipping list',
  tags: ['Shipping'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  query: {
    type: 'object',
    additionalProperties: false,
    properties: {
      sortBy: {
        type: 'string',
        enum: ['name', 'enterprise_name', 'shipping_net_weight', 'shipping_date']
      },
      page: {
        type: 'integer',
        default: 1,
        errorMessage: {
          _: MESSAGE.MSG_PAGE_INDEX_INVALID,
        }
      },
      limit: {
        type: 'integer',
        default: 10,
        errorMessage: {
          _: MESSAGE.MSG_PAGE_SIZE_INVALID,
        }
      },
      destination: {
        type: 'string',
      },
      enterpriseName: {
        type: 'string',
      },
      code: {
        type: 'string',
        pattern: '^\\d+$',
        errorMessage: {
          _: MESSAGE.MSG_LIMITS_PHONENUMBER_ERROR,
        },
      },
      licenseNumber: {
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.LICENSE_NUMBER_ERROR,
        },
      },
      startDate: {
        type: 'string',
        format: 'slash-date',
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        },
      },
      endDate: {
        type: 'string',
        format: 'slash-date',
        formatMinimum: {
          $data: '1/startDate',
        },
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_DATE_ERROR,
          formatMinimum: 'End date must be greater than or equal to start date',
        },
      },
      note1: {
        type: 'string'
      },
      note2: {
        type: 'string'
      }
    }
  }
};

const getShippingDetailSchema = {
  summary: 'Get shipping detail',
  description: 'Get shipping detail',
  tags: ['Shipping'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
};

const cancelShippingSchema = {
  summary: 'Cancel shipping',
  description: 'Cancel shipping',
  tags: ['Shipping'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
};

const editShippingSchema = {
  summary: 'Edit shipping',
  description: 'Edit shipping',
  tags: ['Shipping'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  body: {
    type: 'object',
    additionalProperties: false,
    properties: {
      shippingGrossWeight: {
        type: 'number',
        exclusiveMaximum: 1000000,
      },
      shippingTareWeight: {
        type: 'number',
        exclusiveMaximum: {
          $data: '1/shippingGrossWeight',
        },
      },
      shippingQuantity: {
        type: 'integer'
      },
      shippingDate: {
        type: 'string'
      },
      destinationId: {
        type: 'integer'
      },
      typeDiff: {
        type: 'number',
        enum: [
          TYPE_DIFFERENCE_WEIGHT_ENUM.DEATH,
          TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR,
          TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER,
        ],
        errorMessage: {
          _: MESSAGE.TYPE_DIFFERENCE_REQUIRED,
        },
      },
      reasonDiff: {
        type: 'string',
        minLength: 1,
        maxLength: 300,
        errorMessage: {
          minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
          _: MESSAGE.REASON_DIFFERENCE_INVALID,
        },
      },
    }
  }
};

const exportShippingListSchema = {
  summary: 'Export shipping list',
  description: 'Export shipping list',
  tags: ['Shipping'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  query: {
    type: 'object',
    additionalProperties: false,
    properties: {
      id: {
        type: 'integer'
      },
      destination: {
        type: 'string',
      },
      enterpriseName: {
        type: 'string',
      },
      code: {
        type: 'string',
      },
      licenseNumber: {
        type: 'string'
      },
      sortBy: {
        type: 'string',
        enum: ['name', 'enterprise_name', 'shipping_net_weight', 'shipping_date', '']
      },
      startDate: {
        type: 'string',
        format: 'slash-date',
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        },
      },
      endDate: {
        type: 'string',
        format: 'slash-date',
        formatMinimum: {
          $data: '1/startDate',
        },
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_DATE_ERROR,
          formatMinimum: 'End date must be greater than or equal to start date',
        },
      },
      note1: {
        type: 'string'
      },
      note2: {
        type: 'string'
      }
    }
  }
};

const exportShippingDetailSchema = {
  summary: 'Export shipping detail',
  description: 'Export shipping detail',
  tags: ['Shipping'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  query: {
    type: 'object',
    additionalProperties: false,
    required: ['id'],
    properties: {
      id: {
        type: 'integer'
      }
    }
  }
};

const getExportTodaySchema = {
  summary: 'Get export today',
  description: 'Get export today',
  tags: ['Shipping'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
};

const resetExportTodaySchema = {
  summary: 'Reset export today',
  description: 'Reset export today',
  tags: ['Shipping'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
};

module.exports = {
  getShippingListSchema,
  getShippingDetailSchema,
  cancelShippingSchema,
  editShippingSchema,
  exportShippingListSchema,
  getExportTodaySchema,
  resetExportTodaySchema,
  exportShippingDetailSchema,
};
